# Enhanced Individual Case Page - Comprehensive Redesign

## 📋 Executive Summary

### Current State Analysis
- **Existing**: Case viewing/tracking functionality with ProcessSteps component
- **Gap**: No dedicated new case creation flow with integrated checklist functionality  
- **Opportunity**: Seamless integration of checklist.md requirements into existing architecture

### Redesign Objectives
The redesigned individual Case page will be a **comprehensive case management interface** that combines:
- **Multi-step form creation** for new visa applications
- **Integrated checklist functionality** with 4-priority system (CRITICAL/RECOMMENDED/OPTIONAL/ENHANCEMENT)
- **Enhanced case tracking** with improved progress visualization
- **Document management** with secure upload and validation
- **Real-time progress tracking** with transaction management

## 🔍 Codebase Integration Analysis

### ✅ Existing Strengths to Preserve
- **Design System**: Consistent UI components (Button, Badge, Card, Form) with gorgonzolaBlue (#404BD0) theme
- **Form Patterns**: React Hook Form + Zod validation across all components
- **Authentication**: NextAuth.js integration with proper session management
- **Mock Data**: Well-defined TypeScript interfaces for visa applications
- **Grid Layout**: Responsive 5-column statistics grid and card-based layouts

### 🔧 Integration Points Identified
- **ProcessSteps Component**: Existing expandable step tracking (252 lines)
- **ImmigrationDashboard**: Main dashboard with statistics cards and cases table
- **Mock Data Structure**: VisaApplication interface with steps, documents, timeline
- **Form Components**: GuestImm, LoginImm with established validation patterns
- **Navigation**: ImmigrationTabs with Applications/Document Vault/Services structure

## 🏗️ Enhanced Page Architecture

### Layout Structure (Grid-Based Inheritance)
```
┌─────────────────────────────────────────────────────────────┐
│ Header: Progress Indicator (5 Steps) + Transaction Info     │
├─────────────────┬───────────────────────────────────────────┤
│ Left Sidebar    │ Main Content Area                         │
│ - Navigation    │ ┌─────────────────────────────────────┐   │
│ - Quick Actions │ │ Step Content (Dynamic)              │   │
│ - Case Info     │ │ - Form Fields                       │   │
│                 │ │ - Checklist Integration             │   │
│                 │ │ - Document Upload                   │   │
│                 │ └─────────────────────────────────────┘   │
│                 │ ┌─────────────────────────────────────┐   │
│                 │ │ Navigation Controls                 │   │
│                 │ │ [Previous] [Save] [Next/Submit]     │   │
│                 │ └─────────────────────────────────────┘   │
└─────────────────┴───────────────────────────────────────────┘
```

### Component Hierarchy
- **CasePageContainer** (New)
  - **CaseHeader** (Enhanced from existing)
  - **CaseProgressIndicator** (New - 5 steps)
  - **CaseSidebar** (Enhanced from existing navigation)
  - **CaseMainContent** (New)
    - **StepRenderer** (New - dynamic step content)
    - **ChecklistIntegration** (New - priority-based)
    - **DocumentManager** (Enhanced from existing)
    - **NavigationControls** (New)

## 📝 Multi-Step Form with Checklist Integration

### Enhanced 12-Step Immigration Workflow Integration

#### **Phase 1: Application Creation (Steps 1-5)**
1. **Personal Information** + Checklist Validation
2. **Visa/Preclearance Details** + Document Requirements
3. **Additional Information** + Dynamic Field Logic
4. **Document Upload** + Checklist Verification
5. **Review and Submit** + Final Validation

#### **Phase 2: Professional Processing (Steps 6-8)**
6. **Onboarding** - Client orientation and case assignment
7. **Checkpoint Call** - Professional consultation and guidance
8. **Document Review** - Expert document validation and feedback

#### **Phase 3: Application Processing (Steps 9-11)**
9. **Application Filing** - Official submission to authorities
10. **Application Review** - Internal processing and validation
11. **Application Submission** - Final submission to DETE

#### **Phase 4: Decision Timeline (Step 12)**
12. **Timeline to Expect Outcome** - Processing timeline and status tracking

#### **Phase 5: Query Management (Steps 13-14)**
13. **Queries from DETE** - Handle additional information requests
14. **Queries Answered** - Response submission and tracking

#### **Phase 6: Decision & Appeals (Steps 15-17)**
15. **Work Permits Approved or Rejected** - Final decision notification
16. **Appeal** (conditional) - Appeal process initiation if rejected
17. **Appeal Decision** (conditional) - Final appeal outcome

### Checklist Integration Strategy
- **Priority-Based Validation**: CRITICAL (blocking), RECOMMENDED (warnings), OPTIONAL (suggestions)
- **Real-time Feedback**: Dynamic checklist updates based on form inputs
- **Progress Tracking**: Visual indicators for checklist completion
- **Document Mapping**: Automatic checklist item completion on document upload

## 🎨 Design System Compliance

### Visual Consistency Preservation
- **Color Scheme**: gorgonzolaBlue (#404BD0) primary, preciousPersimmon (#FF783E) accent
- **Typography**: Existing font family and sizing hierarchy
- **Component Styling**: Rounded corners (rounded-lg), soft shadows, consistent spacing
- **Grid System**: Responsive breakpoints (sm:grid-cols-2, lg:grid-cols-5)
- **Status Colors**: Green (completed), Blue (in-progress), Yellow (pending), Red (critical)

### Component Pattern Inheritance
```typescript
// Existing Button patterns
<Button className="w-full justify-start gap-3 h-12" size="lg">
  <Plus className="w-5 h-5" />
  Start New Application
</Button>

// Existing Badge patterns  
<Badge variant="secondary" className="bg-green-100 text-green-800">
  <CheckCircle size={12} className="mr-1" />
  Completed
</Badge>

// Existing Card patterns
<Card className="transition-all duration-200 border-blue-300 shadow-md bg-blue-50/30">
  <CardHeader className="cursor-pointer">
    <CardTitle className="text-lg font-semibold">
      Step Title
    </CardTitle>
  </CardHeader>
</Card>
```

## ⚡ Dynamic Functionality & Validation

### Form Validation Strategy
- **Zod Schema Integration**: Extend existing schema patterns for visa application
- **Step-by-Step Validation**: Prevent progression with incomplete critical fields
- **Real-time Feedback**: Immediate validation on field blur/change
- **Checklist Validation**: Automatic validation against document requirements

### Dynamic Field Logic
- **Conditional Rendering**: Show/hide fields based on visa type selection
- **Checklist Updates**: Dynamic requirement updates based on user selections
- **Progress Calculation**: Real-time completion percentage updates
- **Save State Management**: Auto-save functionality with transaction tracking

## 🛡️ Safety & Implementation Plan

### Risk Assessment
- **Low Risk**: UI component enhancements, styling updates
- **Medium Risk**: New form components, validation logic
- **High Risk**: Integration with existing case management, data flow changes

### Implementation Phases
1. **Phase 1**: Create new components without breaking existing functionality
2. **Phase 2**: Integrate checklist functionality with existing mock data
3. **Phase 3**: Enhance existing ProcessSteps component with new features
4. **Phase 4**: Add new case creation flow with full validation

### Safety Measures
- **Incremental Changes**: Small, testable updates with rollback capability
- **Preserve Integration Points**: Maintain all existing API endpoints and auth flows
- **Backward Compatibility**: Ensure existing case viewing functionality remains intact
- **Testing Strategy**: Unit tests for new components, integration tests for form flow

## 📋 Implementation Deliverables

### New Components to Create
1. **CaseFormWizard** - Multi-step form container
2. **ChecklistValidator** - Priority-based validation component
3. **DocumentUploadManager** - Enhanced upload with checklist integration
4. **ProgressIndicator** - 5-step visual progress tracker
5. **StepNavigation** - Previous/Next/Save controls

### Enhanced Components
1. **ProcessSteps** - Add checklist integration and new case creation mode
2. **ImmigrationDashboard** - Add "New Case" action button
3. **CasesTable** - Add case creation status tracking

### Technical Specifications
- **TypeScript Interfaces**: Extend existing VisaApplication type
- **Zod Schemas**: Create comprehensive validation schemas
- **Mock Data**: Extend existing mock data with checklist requirements
- **Routing**: Add new case creation routes
- **State Management**: Context for form state and checklist progress

This redesign maintains 100% compatibility with existing code while adding comprehensive new case creation functionality with integrated checklist management.

## 🔧 Technical Implementation Details

### Enhanced TypeScript Interfaces for 12-Step Workflow
```typescript
// Extended ApplicationStep interface for 12-step workflow
interface ExtendedApplicationStep {
  id: number;
  title: string;
  description: string;
  phase: 'creation' | 'processing' | 'application' | 'timeline' | 'queries' | 'decision';
  status: 'pending' | 'in_progress' | 'completed' | 'blocked' | 'not_applicable';
  stepType: 'user_action' | 'professional_action' | 'system_action' | 'external_action';
  estimatedDuration?: string;
  actualDuration?: string;
  completedDate?: string;
  scheduledDate?: string;
  assignedTo?: string;
  details: WorkflowStepDetails;
  dependencies?: number[];
  isConditional?: boolean;
  conditionalLogic?: ConditionalLogic;
}

interface WorkflowStepDetails {
  // Creation phase details
  formData?: CaseFormData;
  checklistProgress?: ChecklistProgress;
  validationErrors?: ValidationError[];

  // Processing phase details
  onboardingInfo?: OnboardingDetails;
  checkpointCall?: CheckpointCallDetails;
  documentReview?: DocumentReviewDetails;

  // Application phase details
  filingInfo?: FilingDetails;
  submissionInfo?: SubmissionDetails;

  // Timeline phase details
  timelineInfo?: TimelineDetails;

  // Query phase details
  queries?: QueryDetails[];
  responses?: QueryResponse[];

  // Decision phase details
  decision?: DecisionDetails;
  appealInfo?: AppealDetails;
}

interface OnboardingDetails {
  assignedSpecialist: string;
  onboardingDate: string;
  completionStatus: 'pending' | 'completed';
  documentsProvided: string[];
  nextSteps: string[];
}

interface CheckpointCallDetails {
  callType: 'Video Consultation' | 'Phone Call' | 'In-Person Meeting';
  duration: string;
  specialist: string;
  status: 'scheduled' | 'in_progress' | 'completed' | 'rescheduled';
  scheduledDate: string;
  completedDate?: string;
  agenda: string[];
  meetingLink?: string;
  notes?: string;
  actionItems?: ActionItem[];
}

interface DocumentReviewDetails {
  reviewedBy: string;
  reviewDate: string;
  status: 'under_review' | 'approved' | 'requires_changes' | 'rejected';
  feedback: ReviewFeedback[];
  approvedDocuments: string[];
  rejectedDocuments: string[];
  missingDocuments: string[];
}

interface TimelineDetails {
  estimatedProcessingTime: string;
  currentPhase: string;
  milestones: Milestone[];
  expectedDecisionDate: string;
  lastUpdated: string;
}

interface QueryDetails {
  queryId: string;
  fromDETE: boolean;
  queryDate: string;
  subject: string;
  description: string;
  priority: 'high' | 'medium' | 'low';
  dueDate: string;
  status: 'pending' | 'answered' | 'overdue';
  attachments?: string[];
}

interface DecisionDetails {
  decisionType: 'approved' | 'rejected' | 'conditional_approval';
  decisionDate: string;
  decisionBy: string;
  reasonCode?: string;
  conditions?: string[];
  appealEligible: boolean;
  appealDeadline?: string;
  permitDetails?: PermitDetails;
}

interface ConditionalLogic {
  condition: string;
  dependsOn: number[];
  showIf: (stepData: any) => boolean;
}
```

### Zod Validation Schemas
```typescript
// Personal Information Schema
const personalInfoSchema = z.object({
  surname: z.string().min(1, "Surname is required"),
  forename: z.string().min(1, "Forename is required"),
  otherName: z.string().optional(),
  dateOfBirth: z.string().regex(/^\d{2}\/\d{2}\/\d{4}$/, "Date must be dd/mm/yyyy"),
  gender: z.enum(["Male", "Female"]),
  countryOfBirth: z.string().min(1, "Country of birth is required"),
  currentLocation: z.string().min(1, "Current location is required"),
  address: z.object({
    line1: z.string().min(1, "Address line 1 is required"),
    line2: z.string().optional(),
    line3: z.string().optional(),
    line4: z.string().optional(),
  }),
  contactPhone: z.string().min(1, "Contact phone is required"),
  contactEmail: z.string().email("Valid email is required"),
});

// Visa Details Schema
const visaDetailsSchema = z.object({
  countryOfNationality: z.string().min(1, "Country of nationality is required"),
  reasonForTravel: z.string().min(1, "Reason for travel is required"),
  visaType: z.enum(["Short Stay (C)", "Long Stay (D)"]),
  journeyType: z.enum(["Single", "Multiple"]),
  purposeOfTravel: z.string().min(1, "Purpose of travel is required"),
  passportType: z.string().min(1, "Passport type is required"),
  passportNumber: z.string().min(1, "Passport number is required"),
  issuingAuthority: z.string().min(1, "Issuing authority is required"),
  dateOfIssue: z.string().regex(/^\d{2}\/\d{2}\/\d{4}$/, "Date must be dd/mm/yyyy"),
  dateOfExpiry: z.string().regex(/^\d{2}\/\d{2}\/\d{4}$/, "Date must be dd/mm/yyyy"),
  proposedDates: z.object({
    from: z.string().regex(/^\d{2}\/\d{2}\/\d{4}$/, "Date must be dd/mm/yyyy"),
    to: z.string().regex(/^\d{2}\/\d{2}\/\d{4}$/, "Date must be dd/mm/yyyy"),
  }),
});
```

### Component Architecture
```typescript
// Main Case Form Wizard Component
interface CaseFormWizardProps {
  mode: 'create' | 'edit' | 'view';
  caseId?: string;
  onSave: (data: CaseFormData) => void;
  onSubmit: (data: CaseFormData) => void;
  onCancel: () => void;
}

// Checklist Integration Component
interface ChecklistValidatorProps {
  formData: Partial<CaseFormData>;
  visaType: string;
  onChecklistUpdate: (progress: ChecklistProgress) => void;
  mode: 'validation' | 'display';
}

// Step Renderer Component
interface StepRendererProps {
  step: number;
  formData: CaseFormData;
  checklistProgress: ChecklistProgress;
  onFieldChange: (field: string, value: any) => void;
  onStepComplete: (stepData: any) => void;
  validationErrors: Record<string, string>;
}
```

## 🎯 Integration with Existing Dashboard

### Dashboard Enhancement Points
1. **Quick Actions Section**: Add "Start New Application" button
2. **Statistics Cards**: Include draft applications count
3. **Cases Table**: Add status column for draft/in-progress applications
4. **Navigation**: Seamless transition between case creation and viewing

### Routing Strategy
```typescript
// New routes to add
/profile/application/new - New case creation
/profile/application/new/[step] - Specific step in creation
/profile/application/[caseId]/edit - Edit existing case
/profile/application/[caseId] - View case (existing)
```

### State Management
```typescript
// Case Form Context
interface CaseFormContextType {
  formData: CaseFormData;
  currentStep: number;
  checklistProgress: ChecklistProgress;
  validationState: ValidationState;
  isDirty: boolean;
  updateFormData: (data: Partial<CaseFormData>) => void;
  nextStep: () => void;
  previousStep: () => void;
  saveProgress: () => Promise<void>;
  submitApplication: () => Promise<void>;
}
```

## 📊 Checklist Integration Mapping

### Priority-Based Validation Rules
- **CRITICAL**: Block progression, show error state
- **RECOMMENDED**: Show warning, allow progression with confirmation
- **OPTIONAL**: Show suggestion, no blocking
- **ENHANCEMENT**: Show tip, purely informational

### Document Type Mapping
```typescript
const documentChecklistMapping = {
  'Tourist Visa': {
    critical: ['passport', 'photos', 'financial_proof', 'travel_insurance'],
    recommended: ['employment_letter', 'travel_history'],
    optional: ['invitation_letter', 'accommodation_proof'],
    enhancement: ['return_ticket', 'property_documents']
  },
  'Business Visa': {
    critical: ['passport', 'photos', 'business_invitation', 'financial_proof'],
    recommended: ['company_registration', 'previous_travel'],
    optional: ['conference_documentation'],
    enhancement: ['company_profile', 'bank_guarantee']
  }
  // ... additional visa types
};
```

## 🚀 Implementation Timeline

### Phase 1: Foundation (Week 1-2)
- Create base components and interfaces
- Set up routing and navigation
- Implement basic form structure

### Phase 2: Core Functionality (Week 3-4)
- Implement multi-step form logic
- Add validation and error handling
- Create checklist integration

### Phase 3: Enhancement (Week 5-6)
- Add document upload functionality
- Implement save/draft functionality
- Add progress tracking

### Phase 4: Integration & Testing (Week 7-8)
- Integrate with existing dashboard
- Comprehensive testing
- Performance optimization

This comprehensive redesign ensures seamless integration with existing code while providing enhanced functionality for case creation and management.

## 🎨 Detailed Component Designs

### 1. Enhanced ImmigrationDashboard Integration
```typescript
// Add to existing Quick Actions section
const EnhancedQuickActions: React.FC = () => (
  <div className="bg-white rounded-lg shadow-md border p-6">
    <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
      <Plus className="w-5 h-5 text-blue-600" />
      Quick Actions
    </h3>
    <div className="space-y-3">
      {/* NEW: Start New Application Button */}
      <Button
        className="w-full justify-start gap-3 h-12 bg-gorgonzolaBlue hover:bg-gorgonzolaBlue/90"
        size="lg"
        onClick={() => router.push('/profile/application/new')}
      >
        <Plus className="w-5 h-5" />
        Start New Application
      </Button>

      {/* Existing buttons... */}
      <Button variant="outline" className="w-full justify-start gap-3 h-12" size="lg">
        <AlertTriangle className="w-5 h-5 text-orange-500" />
        View Flagged Items
        <Badge variant="destructive" className="ml-auto">3</Badge>
      </Button>
    </div>
  </div>
);
```

### 2. Enhanced CaseWorkflowManager Component
```typescript
const CaseWorkflowManager: React.FC<CaseWorkflowManagerProps> = ({
  mode = 'create',
  caseId,
  onSave,
  onSubmit,
  onCancel
}) => {
  const [currentPhase, setCurrentPhase] = useState<WorkflowPhase>('creation');
  const [currentStep, setCurrentStep] = useState(1);
  const [workflowSteps, setWorkflowSteps] = useState<ExtendedApplicationStep[]>(initialWorkflowSteps);
  const [formData, setFormData] = useState<CaseFormData>(initialFormData);

  // Group steps by phase for better organization
  const stepsByPhase = useMemo(() => {
    return workflowSteps.reduce((acc, step) => {
      if (!acc[step.phase]) acc[step.phase] = [];
      acc[step.phase].push(step);
      return acc;
    }, {} as Record<WorkflowPhase, ExtendedApplicationStep[]>);
  }, [workflowSteps]);

  return (
    <div className="container mx-auto px-4 py-8 max-w-7xl">
      {/* Enhanced Progress Header with Phase Navigation */}
      <WorkflowProgressIndicator
        currentPhase={currentPhase}
        currentStep={currentStep}
        workflowSteps={workflowSteps}
        stepsByPhase={stepsByPhase}
      />

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 mt-8">
        {/* Enhanced Sidebar with Phase Navigation */}
        <div className="lg:col-span-1">
          <WorkflowSidebar
            currentPhase={currentPhase}
            currentStep={currentStep}
            stepsByPhase={stepsByPhase}
            onPhaseChange={setCurrentPhase}
            onStepChange={setCurrentStep}
          />
        </div>

        {/* Main Workflow Area */}
        <div className="lg:col-span-3">
          <Card className="min-h-[700px]">
            <CardContent className="p-6">
              {/* Phase-specific content rendering */}
              {currentPhase === 'creation' && (
                <CreationPhaseRenderer
                  currentStep={currentStep}
                  formData={formData}
                  onFieldChange={handleFieldChange}
                  onStepComplete={handleStepComplete}
                />
              )}

              {currentPhase === 'processing' && (
                <ProcessingPhaseRenderer
                  currentStep={currentStep}
                  workflowData={workflowSteps}
                  onStatusUpdate={handleStatusUpdate}
                />
              )}

              {currentPhase === 'application' && (
                <ApplicationPhaseRenderer
                  currentStep={currentStep}
                  applicationData={workflowSteps}
                  onSubmissionUpdate={handleSubmissionUpdate}
                />
              )}

              {currentPhase === 'timeline' && (
                <TimelinePhaseRenderer
                  timelineData={getTimelineData()}
                  milestones={getMilestones()}
                />
              )}

              {currentPhase === 'queries' && (
                <QueryPhaseRenderer
                  queries={getQueries()}
                  onQueryResponse={handleQueryResponse}
                />
              )}

              {currentPhase === 'decision' && (
                <DecisionPhaseRenderer
                  decisionData={getDecisionData()}
                  appealOptions={getAppealOptions()}
                  onAppealSubmit={handleAppealSubmit}
                />
              )}
            </CardContent>
          </Card>

          {/* Phase-specific Navigation Controls */}
          <WorkflowNavigation
            currentPhase={currentPhase}
            currentStep={currentStep}
            workflowSteps={workflowSteps}
            onPrevious={handlePrevious}
            onNext={handleNext}
            onSave={handleSave}
            onSubmit={handleSubmit}
            canProceed={validateCurrentStep()}
          />
        </div>
      </div>
    </div>
  );
};
```

### 3. CaseProgressIndicator Component
```typescript
const CaseProgressIndicator: React.FC<{
  currentStep: number;
  totalSteps: number;
  checklistProgress: ChecklistProgress;
}> = ({ currentStep, totalSteps, checklistProgress }) => {
  const steps = [
    { id: 1, title: "Personal Information", icon: User },
    { id: 2, title: "Visa Details", icon: FileText },
    { id: 3, title: "Additional Information", icon: Info },
    { id: 4, title: "Document Upload", icon: Upload },
    { id: 5, title: "Review & Submit", icon: CheckCircle }
  ];

  return (
    <Card className="bg-gradient-to-r from-blue-50 to-blue-100 border-blue-200">
      <CardContent className="p-6">
        {/* Transaction Info */}
        <div className="flex justify-between items-center mb-6">
          <div>
            <h2 className="text-xl font-bold text-gray-900">New Visa Application</h2>
            <p className="text-sm text-gray-600">Transaction ID: TXN-{Date.now()}</p>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold text-blue-600">
              {Math.round((currentStep / totalSteps) * 100)}%
            </div>
            <div className="text-sm text-gray-600">Complete</div>
          </div>
        </div>

        {/* Progress Steps */}
        <div className="flex items-center justify-between">
          {steps.map((step, index) => {
            const isCompleted = step.id < currentStep;
            const isCurrent = step.id === currentStep;
            const Icon = step.icon;

            return (
              <div key={step.id} className="flex items-center">
                <div className="flex flex-col items-center">
                  <div className={`
                    flex items-center justify-center w-10 h-10 rounded-full border-2
                    ${isCurrent
                      ? 'border-blue-600 bg-blue-100'
                      : isCompleted
                        ? 'border-green-600 bg-green-100'
                        : 'border-gray-300 bg-white'
                    }
                  `}>
                    <Icon size={20} className={
                      isCurrent ? 'text-blue-600' :
                      isCompleted ? 'text-green-600' : 'text-gray-400'
                    } />
                  </div>
                  <div className="text-xs text-center mt-2 max-w-[80px]">
                    {step.title}
                  </div>
                </div>

                {/* Connector Line */}
                {index < steps.length - 1 && (
                  <div className={`
                    flex-1 h-0.5 mx-4
                    ${isCompleted ? 'bg-green-300' : 'bg-gray-200'}
                  `} />
                )}
              </div>
            );
          })}
        </div>

        {/* Checklist Summary */}
        <div className="mt-6 grid grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-lg font-bold text-red-600">
              {checklistProgress.critical.filter(item => item.status === 'pending').length}
            </div>
            <div className="text-xs text-gray-600">Critical Pending</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-yellow-600">
              {checklistProgress.recommended.filter(item => item.status === 'pending').length}
            </div>
            <div className="text-xs text-gray-600">Recommended</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-blue-600">
              {checklistProgress.optional.filter(item => item.status === 'pending').length}
            </div>
            <div className="text-xs text-gray-600">Optional</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-green-600">
              {checklistProgress.completionPercentage}%
            </div>
            <div className="text-xs text-gray-600">Complete</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
```

## 🔄 Enhanced ProcessSteps Integration for 12-Step Workflow

### Extended ProcessSteps Component Architecture
```typescript
interface EnhancedProcessStepsProps {
  workflowSteps: ExtendedApplicationStep[];
  currentPhase: WorkflowPhase;
  currentStep: number;
  caseStatus: string;
  mode?: 'view' | 'create' | 'track';
  onStepEdit?: (stepId: number) => void;
  onPhaseChange?: (phase: WorkflowPhase) => void;
  checklistProgress?: ChecklistProgress;
  groupByPhase?: boolean;
}

const EnhancedProcessSteps: React.FC<EnhancedProcessStepsProps> = ({
  workflowSteps,
  currentPhase,
  currentStep,
  caseStatus,
  mode = 'view',
  onStepEdit,
  onPhaseChange,
  checklistProgress,
  groupByPhase = true
}) => {
  // Group steps by phase for better organization
  const stepsByPhase = useMemo(() => {
    return workflowSteps.reduce((acc, step) => {
      if (!acc[step.phase]) acc[step.phase] = [];
      acc[step.phase].push(step);
      return acc;
    }, {} as Record<WorkflowPhase, ExtendedApplicationStep[]>);
  }, [workflowSteps]);

  const phaseConfig = {
    creation: {
      title: 'Application Creation',
      icon: FileText,
      color: 'blue',
      description: 'Complete your application form and upload documents'
    },
    processing: {
      title: 'Professional Processing',
      icon: Users,
      color: 'purple',
      description: 'Expert review and consultation services'
    },
    application: {
      title: 'Application Submission',
      icon: Send,
      color: 'orange',
      description: 'Official filing and submission to authorities'
    },
    timeline: {
      title: 'Processing Timeline',
      icon: Clock,
      color: 'yellow',
      description: 'Track expected processing times and milestones'
    },
    queries: {
      title: 'Query Management',
      icon: MessageSquare,
      color: 'indigo',
      description: 'Handle additional information requests'
    },
    decision: {
      title: 'Decision & Appeals',
      icon: CheckCircle,
      color: 'green',
      description: 'Final decision and appeal process if needed'
    }
  };

  if (groupByPhase) {
    return (
      <div className="space-y-8">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold text-gray-900">Immigration Workflow</h2>
          <div className="flex gap-2">
            <Badge variant="outline" className="text-xs">
              {mode === 'create' ? 'Creation Mode' : 'Tracking Mode'}
            </Badge>
            <Badge variant="secondary" className="text-xs">
              Phase: {phaseConfig[currentPhase]?.title}
            </Badge>
          </div>
        </div>

        {/* Phase Navigation Tabs */}
        <div className="flex flex-wrap gap-2 mb-6">
          {Object.entries(phaseConfig).map(([phase, config]) => {
            const phaseSteps = stepsByPhase[phase as WorkflowPhase] || [];
            const isActive = currentPhase === phase;
            const isCompleted = phaseSteps.every(step => step.status === 'completed');
            const hasSteps = phaseSteps.length > 0;

            if (!hasSteps && phase !== 'creation') return null;

            return (
              <Button
                key={phase}
                variant={isActive ? "default" : "outline"}
                size="sm"
                className={`flex items-center gap-2 ${
                  isActive ? 'bg-gorgonzolaBlue' : ''
                } ${isCompleted ? 'border-green-500' : ''}`}
                onClick={() => onPhaseChange?.(phase as WorkflowPhase)}
              >
                <config.icon size={16} />
                {config.title}
                {isCompleted && <CheckCircle size={14} className="text-green-500" />}
              </Button>
            );
          })}
        </div>

        {/* Current Phase Steps */}
        <div className="space-y-4">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 className="font-semibold text-blue-900 mb-2">
              {phaseConfig[currentPhase]?.title}
            </h3>
            <p className="text-sm text-blue-700">
              {phaseConfig[currentPhase]?.description}
            </p>
          </div>

          {/* Render steps for current phase */}
          <div className="space-y-3">
            {(stepsByPhase[currentPhase] || []).map((step, index) => (
              <WorkflowStepCard
                key={step.id}
                step={step}
                isCurrentStep={step.id === currentStep}
                isLastInPhase={index === (stepsByPhase[currentPhase]?.length || 0) - 1}
                checklistItems={getChecklistForStep(step.id, checklistProgress)}
                onEdit={() => onStepEdit?.(step.id)}
                mode={mode}
              />
            ))}
          </div>
        </div>

        {/* Conditional Appeal Steps */}
        {currentPhase === 'decision' && shouldShowAppealSteps(workflowSteps) && (
          <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <h4 className="font-semibold text-yellow-900 mb-2">Appeal Process</h4>
            <p className="text-sm text-yellow-700 mb-3">
              If your application is rejected, you may be eligible to appeal the decision.
            </p>
            <div className="space-y-2">
              {getAppealSteps(workflowSteps).map((step) => (
                <WorkflowStepCard
                  key={step.id}
                  step={step}
                  isCurrentStep={step.id === currentStep}
                  isLastInPhase={false}
                  onEdit={() => onStepEdit?.(step.id)}
                  mode={mode}
                />
              ))}
            </div>
          </div>
        )}
      </div>
    );
  }

  // Linear view for simpler cases
  return (
    <div className="space-y-4">
      {workflowSteps.map((step, index) => (
        <WorkflowStepCard
          key={step.id}
          step={step}
          isCurrentStep={step.id === currentStep}
          isLastInPhase={index === workflowSteps.length - 1}
          checklistItems={getChecklistForStep(step.id, checklistProgress)}
          onEdit={() => onStepEdit?.(step.id)}
          mode={mode}
        />
      ))}
    </div>
  );
};
```

### WorkflowStepCard Component
```typescript
const WorkflowStepCard: React.FC<{
  step: ExtendedApplicationStep;
  isCurrentStep: boolean;
  isLastInPhase: boolean;
  checklistItems?: ChecklistItem[];
  onEdit?: () => void;
  mode: 'view' | 'create' | 'track';
}> = ({ step, isCurrentStep, isLastInPhase, checklistItems, onEdit, mode }) => {
  const getStatusIcon = () => {
    switch (step.status) {
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-600" />;
      case 'in_progress':
        return <Clock className="w-5 h-5 text-blue-600" />;
      case 'blocked':
        return <AlertTriangle className="w-5 h-5 text-red-600" />;
      case 'pending':
      default:
        return <Circle className="w-5 h-5 text-gray-400" />;
    }
  };

  const getStatusColor = () => {
    switch (step.status) {
      case 'completed':
        return 'border-green-300 bg-green-50';
      case 'in_progress':
        return 'border-blue-300 bg-blue-50';
      case 'blocked':
        return 'border-red-300 bg-red-50';
      case 'pending':
      default:
        return 'border-gray-200 bg-white';
    }
  };

  const getStepTypeLabel = () => {
    switch (step.stepType) {
      case 'user_action':
        return 'Your Action Required';
      case 'professional_action':
        return 'Professional Review';
      case 'system_action':
        return 'System Processing';
      case 'external_action':
        return 'External Processing';
      default:
        return '';
    }
  };

  return (
    <Card className={`transition-all duration-200 ${getStatusColor()} ${
      isCurrentStep ? 'ring-2 ring-blue-500 shadow-lg' : 'shadow-md'
    }`}>
      <CardHeader className="cursor-pointer" onClick={onEdit}>
        <div className="flex items-start justify-between">
          <div className="flex items-start gap-3">
            {getStatusIcon()}
            <div className="flex-1">
              <CardTitle className="text-lg font-semibold flex items-center gap-2">
                {step.title}
                {step.stepType && (
                  <Badge variant="outline" className="text-xs">
                    {getStepTypeLabel()}
                  </Badge>
                )}
              </CardTitle>
              <p className="text-sm text-gray-600 mt-1">{step.description}</p>

              {/* Timeline Information */}
              {step.estimatedDuration && (
                <div className="flex items-center gap-4 mt-2 text-xs text-gray-500">
                  <span>Est. Duration: {step.estimatedDuration}</span>
                  {step.scheduledDate && (
                    <span>Scheduled: {new Date(step.scheduledDate).toLocaleDateString()}</span>
                  )}
                  {step.completedDate && (
                    <span>Completed: {new Date(step.completedDate).toLocaleDateString()}</span>
                  )}
                </div>
              )}
            </div>
          </div>

          <div className="flex items-center gap-2">
            {step.assignedTo && (
              <Badge variant="secondary" className="text-xs">
                {step.assignedTo}
              </Badge>
            )}
            {mode === 'create' && step.stepType === 'user_action' && (
              <Button size="sm" variant="outline">
                Edit
              </Button>
            )}
          </div>
        </div>
      </CardHeader>

      {/* Expandable Content */}
      {isCurrentStep && (
        <CardContent className="pt-0">
          {/* Step-specific content based on phase */}
          {step.phase === 'processing' && step.details.checkpointCall && (
            <CheckpointCallDetails call={step.details.checkpointCall} />
          )}

          {step.phase === 'timeline' && step.details.timelineInfo && (
            <TimelineVisualization timeline={step.details.timelineInfo} />
          )}

          {step.phase === 'queries' && step.details.queries && (
            <QueryManagement queries={step.details.queries} />
          )}

          {step.phase === 'decision' && step.details.decision && (
            <DecisionDetails decision={step.details.decision} />
          )}

          {/* Checklist Integration */}
          {checklistItems && checklistItems.length > 0 && (
            <div className="mt-4 p-3 bg-gray-50 rounded-lg">
              <h4 className="font-medium text-sm mb-2">Requirements Checklist</h4>
              <div className="space-y-1">
                {checklistItems.map((item) => (
                  <div key={item.id} className="flex items-center gap-2 text-sm">
                    <div className={`w-2 h-2 rounded-full ${
                      item.status === 'completed' ? 'bg-green-500' :
                      item.status === 'pending' ? 'bg-yellow-500' : 'bg-gray-300'
                    }`} />
                    <span className={item.priority === 'CRITICAL' ? 'font-medium' : ''}>
                      {item.title}
                    </span>
                    <Badge variant="outline" className="text-xs">
                      {item.priority}
                    </Badge>
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      )}
    </Card>
  );
};
```

### Timeline Visualization Component
```typescript
const TimelineVisualization: React.FC<{ timeline: TimelineDetails }> = ({ timeline }) => {
  return (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div className="p-3 bg-blue-50 rounded-lg">
          <div className="text-sm font-medium text-blue-900">Processing Time</div>
          <div className="text-lg font-bold text-blue-700">{timeline.estimatedProcessingTime}</div>
        </div>
        <div className="p-3 bg-green-50 rounded-lg">
          <div className="text-sm font-medium text-green-900">Expected Decision</div>
          <div className="text-lg font-bold text-green-700">
            {new Date(timeline.expectedDecisionDate).toLocaleDateString()}
          </div>
        </div>
      </div>

      <div className="space-y-3">
        <h4 className="font-medium">Milestones</h4>
        {timeline.milestones.map((milestone, index) => (
          <div key={index} className="flex items-center gap-3 p-2 border rounded-lg">
            <div className={`w-3 h-3 rounded-full ${
              milestone.completed ? 'bg-green-500' : 'bg-gray-300'
            }`} />
            <div className="flex-1">
              <div className="font-medium text-sm">{milestone.title}</div>
              <div className="text-xs text-gray-600">{milestone.description}</div>
            </div>
            <div className="text-xs text-gray-500">
              {milestone.expectedDate && new Date(milestone.expectedDate).toLocaleDateString()}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
```

This design maintains complete backward compatibility while adding comprehensive 12-step workflow functionality with integrated checklist management and timeline visualization.

## 🧪 Testing Strategy

### Unit Testing Requirements
```typescript
// Component Testing with Jest + React Testing Library
describe('CaseFormWizard', () => {
  it('should render all steps correctly', () => {
    render(<CaseFormWizard mode="create" />);
    expect(screen.getByText('Personal Information')).toBeInTheDocument();
    expect(screen.getByText('Visa Details')).toBeInTheDocument();
  });

  it('should validate critical checklist items', () => {
    const { result } = renderHook(() => useChecklistValidation('Tourist Visa'));
    expect(result.current.criticalItems).toHaveLength(8);
  });

  it('should prevent progression with incomplete critical fields', () => {
    render(<CaseFormWizard mode="create" />);
    const nextButton = screen.getByText('Next');
    expect(nextButton).toBeDisabled();
  });
});

// Integration Testing
describe('Case Creation Flow', () => {
  it('should complete full application flow', async () => {
    const user = userEvent.setup();
    render(<CaseFormWizard mode="create" />);

    // Step 1: Personal Information
    await user.type(screen.getByLabelText('Surname'), 'Doe');
    await user.type(screen.getByLabelText('Forename'), 'John');
    await user.click(screen.getByText('Next'));

    // Verify progression and checklist updates
    expect(screen.getByText('Visa Details')).toBeInTheDocument();
  });
});
```

### Performance Testing
- **Target Metrics**: Page load < 2s, form interaction < 100ms
- **Bundle Size**: Monitor component bundle impact
- **Memory Usage**: Test for memory leaks in multi-step forms

## 📋 Implementation Checklist

### Pre-Implementation Safety Checks
- [ ] **Backup existing code** - Create feature branch
- [ ] **Review integration points** - Identify all dependencies
- [ ] **Test existing functionality** - Ensure no regressions
- [ ] **Validate design system** - Confirm component compatibility

### Phase 1: Foundation Components (12-Step Workflow)
- [ ] Create `CaseWorkflowManager` component structure
- [ ] Implement `WorkflowProgressIndicator` with phase navigation
- [ ] Set up routing for 12-step workflow
- [ ] Create extended TypeScript interfaces (`ExtendedApplicationStep`, `WorkflowStepDetails`)
- [ ] Add unit tests for new workflow components

### Phase 2: Creation Phase Implementation (Steps 1-5)
- [ ] Implement Step 1: Personal Information form
- [ ] Implement Step 2: Visa Details form
- [ ] Implement Step 3: Additional Information form
- [ ] Implement Step 4: Document Upload with validation
- [ ] Implement Step 5: Review and Submit
- [ ] Add form validation with extended Zod schemas
- [ ] Create `CreationPhaseRenderer` component

### Phase 3: Processing Phase Implementation (Steps 6-8)
- [ ] Create `ProcessingPhaseRenderer` component
- [ ] Implement Step 6: Onboarding workflow
- [ ] Implement Step 7: Checkpoint Call scheduling and management
- [ ] Implement Step 8: Document Review with feedback system
- [ ] Add professional assignment and tracking
- [ ] Create status update mechanisms

### Phase 4: Application Phase Implementation (Steps 9-11)
- [ ] Create `ApplicationPhaseRenderer` component
- [ ] Implement Step 9: Application Filing tracking
- [ ] Implement Step 10: Application Review status
- [ ] Implement Step 11: Application Submission to DETE
- [ ] Add external system integration points
- [ ] Create submission tracking and notifications

### Phase 5: Timeline & Query Management (Steps 12-14)
- [ ] Create `TimelinePhaseRenderer` with milestone visualization
- [ ] Implement Step 12: Timeline to Expect Outcome with progress tracking
- [ ] Create `QueryPhaseRenderer` component
- [ ] Implement Step 13: Queries from DETE management
- [ ] Implement Step 14: Query response system
- [ ] Add timeline visualization and milestone tracking

### Phase 6: Decision & Appeals (Steps 15-17)
- [ ] Create `DecisionPhaseRenderer` component
- [ ] Implement Step 15: Work Permits decision display
- [ ] Implement Step 16: Appeal process (conditional)
- [ ] Implement Step 17: Appeal Decision tracking (conditional)
- [ ] Add conditional logic for appeal workflow
- [ ] Create decision notification system

### Phase 7: Enhanced ProcessSteps Integration
- [ ] Enhance `ProcessSteps` component with phase grouping
- [ ] Create `WorkflowStepCard` component
- [ ] Implement phase navigation tabs
- [ ] Add progressive disclosure for 12+ steps
- [ ] Create timeline visualization components
- [ ] Add status badges and progress indicators

### Phase 8: Checklist Integration Across Workflow
- [ ] Map checklist.md requirements to appropriate workflow steps
- [ ] Implement priority-based validation across all phases
- [ ] Add real-time checklist updates for each step
- [ ] Create phase-specific checklist validation
- [ ] Integrate document requirements with workflow steps

### Phase 9: Integration & Testing
- [ ] Integrate with existing `ImmigrationDashboard`
- [ ] Add "Start New Application" workflow entry point
- [ ] Enhance existing case viewing with 12-step tracking
- [ ] Add comprehensive testing for all workflow phases
- [ ] Performance optimization for extended workflow
- [ ] Accessibility compliance (WCAG 2.1 AA) across all steps

## 🚀 Deployment Strategy

### Gradual Rollout Plan
1. **Feature Flag Implementation**: Control new functionality visibility
2. **A/B Testing**: Compare new vs existing user flows
3. **Monitoring**: Track user engagement and completion rates
4. **Feedback Collection**: Gather user feedback for improvements

### Success Metrics
- **User Engagement**: Increased case creation completion rate
- **Error Reduction**: Fewer validation errors and support tickets
- **Performance**: Maintained or improved page load times
- **Accessibility**: 100% WCAG 2.1 AA compliance

## 📊 Risk Mitigation

### Technical Risks
- **Integration Complexity**: Mitigated by incremental implementation
- **Performance Impact**: Addressed through code splitting and optimization
- **Data Consistency**: Ensured through comprehensive validation

### User Experience Risks
- **Learning Curve**: Mitigated by intuitive design and help text
- **Form Abandonment**: Addressed through save/draft functionality
- **Mobile Usability**: Ensured through responsive design testing

## 🎯 Success Criteria

### Functional Requirements
- ✅ **Multi-step form creation** with 5 distinct steps
- ✅ **Integrated checklist validation** with 4-priority system
- ✅ **Document upload management** with validation
- ✅ **Progress tracking** with visual indicators
- ✅ **Save/draft functionality** with transaction management

### Non-Functional Requirements
- ✅ **Performance**: Page load < 2s, interactions < 100ms
- ✅ **Accessibility**: WCAG 2.1 AA compliance
- ✅ **Responsive Design**: Mobile-first approach
- ✅ **Browser Compatibility**: Modern browsers (Chrome, Firefox, Safari, Edge)
- ✅ **Security**: Secure file upload and data handling

### Quality Targets
- ✅ **Code Coverage**: 80% minimum for new components
- ✅ **Type Safety**: 100% TypeScript coverage
- ✅ **Design Consistency**: 100% adherence to existing design system
- ✅ **Backward Compatibility**: 100% preservation of existing functionality

This comprehensive redesign provides a robust foundation for enhanced case management while maintaining the highest standards of code quality, user experience, and system reliability.
