// components/CasesTable.tsx
"use client";

import React from "react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Eye, Clock, CheckCircle, XCircle, FileText } from "lucide-react";

interface Case {
  id: string;
  caseType: string;
  userName: string;
  caseStatus: string;
  priority: string;
  startDate: string;
  endDate: string;
  progress?: number;
  milestones?: {
    submitted: boolean;
    documentsReviewed: boolean;
    interviewScheduled: boolean;
    approved: boolean;
  };
}

interface CasesTableProps {
  cases: Case[];
  currentPage: number;
  itemsPerPage: number;
  onPageChange: (page: number) => void;
}

const getStatusBadgeClass = (status: string) => {
  switch (status.toLowerCase()) {
    case "in review":
      return "bg-yellow-100 text-yellow-800 border-yellow-200";
    case "approved":
      return "bg-green-100 text-green-800 border-green-200";
    case "submitted":
      return "bg-blue-100 text-blue-800 border-blue-200";
    case "rejected":
      return "bg-red-100 text-red-800 border-red-200";
    case "draft":
      return "bg-gray-100 text-gray-800 border-gray-200";
    case "open":
      return "bg-blue-100 text-blue-800 border-blue-200";
    case "closed":
      return "bg-green-100 text-green-800 border-green-200";
    case "pending":
      return "bg-yellow-100 text-yellow-800 border-yellow-200";
    case "in progress":
      return "bg-purple-100 text-purple-800 border-purple-200";
    default:
      return "bg-gray-100 text-gray-800 border-gray-200";
  }
};

const getStatusIcon = (status: string) => {
  switch (status.toLowerCase()) {
    case "in review":
      return <Clock className="w-4 h-4" />;
    case "approved":
      return <CheckCircle className="w-4 h-4" />;
    case "submitted":
      return <FileText className="w-4 h-4" />;
    case "rejected":
      return <XCircle className="w-4 h-4" />;
    case "draft":
      return <FileText className="w-4 h-4" />;
    default:
      return <Clock className="w-4 h-4" />;
  }
};

const getPriorityBadgeClass = (priority: string) => {
  switch (priority.toLowerCase()) {
    case "high":
      return "bg-red-100 text-red-800 border-red-200";
    case "medium":
      return "bg-yellow-100 text-yellow-800 border-yellow-200";
    case "low":
      return "bg-green-100 text-green-800 border-green-200";
    default:
      return "bg-gray-100 text-gray-800 border-gray-200";
  }
};

const CasesTable: React.FC<CasesTableProps> = ({
  cases,
  currentPage,
  itemsPerPage,
  onPageChange,
}) => {
  const router = useRouter();
  const totalPages = Math.ceil(cases.length / itemsPerPage);

  const handleViewDetails = (caseId: string) => {
    router.push(`/profile/application/${caseId}`);
  };

  const paginatedCases = cases.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  const handlePrevPage = () => {
    onPageChange(Math.max(currentPage - 1, 1));
  };

  const handleNextPage = () => {
    onPageChange(Math.min(currentPage + 1, totalPages));
  };

  return (
    <div className="overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Application
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Type & Priority
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Status & Progress
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Timeline
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Actions
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {paginatedCases.map((c, idx) => (
            <tr key={c.id} className="hover:bg-gray-50 transition-colors">
              {/* Application Info */}
              <td className="px-6 py-4">
                <div className="flex flex-col">
                  <div className="text-sm font-medium text-gray-900">{c.id}</div>
                  <div className="text-sm text-gray-500">{c.userName}</div>
                </div>
              </td>

              {/* Type & Priority */}
              <td className="px-6 py-4">
                <div className="flex flex-col gap-2">
                  <div className="text-sm font-medium text-gray-900">{c.caseType}</div>
                  <Badge
                    variant="outline"
                    className={`w-fit text-xs ${getPriorityBadgeClass(c.priority)}`}
                  >
                    {c.priority} Priority
                  </Badge>
                </div>
              </td>

              {/* Status & Progress */}
              <td className="px-6 py-4">
                <div className="flex flex-col gap-2">
                  <Badge
                    variant="outline"
                    className={`w-fit text-xs flex items-center gap-1 ${getStatusBadgeClass(c.caseStatus)}`}
                  >
                    {getStatusIcon(c.caseStatus)}
                    {c.caseStatus}
                  </Badge>
                  {c.progress !== undefined && (
                    <div className="flex items-center gap-2">
                      <Progress value={c.progress} className="w-20 h-2" />
                      <span className="text-xs text-gray-500">{c.progress}%</span>
                    </div>
                  )}
                </div>
              </td>

              {/* Timeline */}
              <td className="px-6 py-4">
                <div className="flex flex-col gap-1">
                  <div className="text-sm text-gray-900">
                    <span className="text-gray-500">Start:</span> {new Date(c.startDate).toLocaleDateString()}
                  </div>
                  <div className="text-sm text-gray-900">
                    <span className="text-gray-500">End:</span> {new Date(c.endDate).toLocaleDateString()}
                  </div>
                </div>
              </td>

              {/* Actions */}
              <td className="px-6 py-4">
                <Button
                  onClick={() => handleViewDetails(c.id)}
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-2 hover:bg-blue-50 hover:border-blue-300"
                >
                  <Eye size={16} />
                  View Details
                </Button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>

      {/* Pagination Controls */}
      <div className="flex justify-between items-center px-6 py-4 bg-gray-50 border-t border-gray-200">
        <Button
          type="button"
          onClick={handlePrevPage}
          disabled={currentPage === 1}
          variant={currentPage === 1 ? "outline" : "default"}
          size="sm"
          className="flex items-center gap-2"
        >
          Previous
        </Button>
        <span className="text-sm text-gray-700 font-medium">
          Page {currentPage} of {totalPages} ({cases.length} total applications)
        </span>
        <Button
          type="button"
          onClick={handleNextPage}
          disabled={currentPage === totalPages}
          variant={currentPage === totalPages ? "outline" : "default"}
          size="sm"
          className="flex items-center gap-2"
        >
          Next
        </Button>
      </div>
    </div>
  );
};

export default CasesTable;
